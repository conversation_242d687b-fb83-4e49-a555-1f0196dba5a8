// Pekka HR - HRMS + Payroll + Travel Management System
// Prisma Schema for comprehensive employee lifecycle management

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Authentication and User Management
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  password      String?
  role          UserRole  @default(EMPLOYEE)
  roleId        String?
  isActive      Boolean   @default(true)
  lastLoginAt   DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  accounts Account[]
  sessions Session[]
  employee Employee?
  customRole Role?   @relation("UserCustomRole", fields: [roleId], references: [id])
  auditLogs AuditLog[]
  approvedTimesheets Timesheet[] @relation("TimesheetApprover")

  @@map("users")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// Role-Based Access Control
model Role {
  id          String   @id @default(cuid())
  name        String   @unique
  code        String   @unique
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  users           User[] @relation("UserCustomRole")
  rolePermissions RolePermission[]

  @@map("roles")
}

model Permission {
  id          String   @id @default(cuid())
  name        String   @unique
  code        String   @unique
  module      String   // HR, PAYROLL, EXPENSE, PERFORMANCE, etc.
  action      String   // CREATE, READ, UPDATE, DELETE, APPROVE, etc.
  resource    String   // EMPLOYEE, ATTENDANCE, LEAVE, PAYROLL, etc.
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  rolePermissions RolePermission[]

  @@unique([module, action, resource])
  @@map("permissions")
}

model RolePermission {
  id           String @id @default(cuid())
  roleId       String
  permissionId String

  role       Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permission Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId])
  @@map("role_permissions")
}

// Audit Logging for Security Tracking
model AuditLog {
  id          String   @id @default(cuid())
  userId      String?
  userName    String?  // Store username for historical reference
  action      String   // LOGIN, LOGOUT, CREATE, UPDATE, DELETE, etc.
  resource    String   // TABLE_NAME or RESOURCE_TYPE
  resourceId  String?  // ID of the affected resource
  details     Json?    // Additional context and details
  oldValues   Json?    // Previous values for updates
  newValues   Json?    // New values for creates/updates
  ipAddress   String?
  userAgent   String?
  sessionId   String?  // Session identifier
  success     Boolean  @default(true) // Whether the action was successful
  errorMessage String? // Error message if action failed
  timestamp   DateTime @default(now())

  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@index([userId])
  @@index([action])
  @@index([resource])
  @@index([timestamp])
  @@index([success])
  @@map("audit_logs")
}

// Core Employee Management
model Employee {
  id           String         @id @default(cuid())
  userId       String         @unique
  employeeCode String         @unique
  firstName    String
  lastName     String
  email        String         @unique
  phone        String?
  dateOfBirth  DateTime?
  gender       Gender?
  address      Json?
  
  // Professional Information
  designation     String
  departmentId    String
  joiningDate     DateTime
  employmentType  EmploymentType @default(FULL_TIME)
  employeeType    EmployeeType   @default(NORMAL)
  status          EmployeeStatus @default(ACTIVE)
  reportingTo     String?
  
  // Salary Information
  basicSalary     Decimal?       @db.Decimal(10, 2)
  ctc             Decimal?       @db.Decimal(12, 2)
  salaryGrade     String?
  
  // Additional fields for compliance
  panNumber       String?        @unique
  aadharNumber    String?        @unique
  pfNumber        String?        @unique
  esiNumber       String?        @unique
  
  // Banking Information
  bankAccountNumber String?
  bankIFSC         String?
  bankName         String?
  bankBranch       String?
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user               User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  department         Department          @relation(fields: [departmentId], references: [id])
  headOfDepartments  Department[]        @relation("DepartmentHead")
  manager            Employee?           @relation("EmployeeHierarchy", fields: [reportingTo], references: [id])
  subordinates       Employee[]          @relation("EmployeeHierarchy")
  
  // Activity Records
  attendanceRecords  AttendanceRecord[]
  attendanceRequests AttendanceRequest[]
  employeeLocations  EmployeeLocation[]
  leaveRequests      LeaveRequest[]
  leaveBalances      LeaveBalance[]
  expenseClaims      ExpenseClaim[]
  payrollRecords     PayrollRecord[]
  performanceReviews PerformanceReview[]
  documents          Document[]
  onboardingWorkflow OnboardingWorkflow?
  
  // Payroll Relations
  salaryStructures   EmployeeSalaryStructure[]
  salaryRevisions    SalaryRevision[]
  payslips           Payslip[]
  
  // Timesheet Relations
  timesheets         Timesheet[]
  
  // Site Relations (for Field Employees)
  siteVisits         SiteVisit[]
  employeeSites      EmployeeSite[]
  
  // Announcement Relations
  announcementViews  AnnouncementView[]
  
  // Distance Tracking Relations
  distanceTrackingPoints DistanceTrackingPoint[]
  dailyDistanceRecords   DailyDistanceRecord[]
  distanceAnomalies      DistanceAnomaly[]
  
  // Travel and Expense Relations
  travelRequests         TravelRequest[]
  monthlyPetrolExpenses  MonthlyPetrolExpense[]
  
  // Performance Management Relations
  objectives             Objective[]
  feedbacksGiven         Feedback[]              @relation("FeedbackReviewer")
  feedbacksReceived      Feedback[]              @relation("FeedbackEmployee")

  @@index([departmentId])
  @@index([status])
  @@index([reportingTo])
  @@index([employeeCode])
  @@map("employees")
}

model Department {
  id          String   @id @default(cuid())
  name        String   @unique
  code        String   @unique
  description String?
  headId      String?
  parentId    String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  employees      Employee[]
  head           Employee?    @relation("DepartmentHead", fields: [headId], references: [id])
  parent         Department?  @relation("DepartmentHierarchy", fields: [parentId], references: [id])
  subDepartments Department[] @relation("DepartmentHierarchy")

  @@index([parentId])
  @@index([headId])
  @@index([isActive])
  @@map("departments")
}

// Attendance Management
model AttendanceRecord {
  id         String           @id @default(cuid())
  employeeId String
  date       DateTime         @db.Date
  checkIn    DateTime?
  checkOut   DateTime?
  location   Json?            // GPS coordinates, office location
  method     AttendanceMethod @default(WEB)
  status     AttendanceStatus @default(PRESENT)
  workHours  Decimal?         @db.Decimal(4, 2)
  overtime   Decimal?         @db.Decimal(4, 2)
  notes      String?
  approvedBy String?          // For manual corrections
  approvedAt DateTime?
  createdAt  DateTime         @default(now())
  updatedAt  DateTime         @updatedAt

  employee Employee @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  checkInOut CheckInOut[]

  @@unique([employeeId, date])
  @@index([employeeId, date])
  @@index([status])
  @@index([method])
  @@map("attendance_records")
}

// Detailed check-in/check-out tracking for multiple entries per day
model CheckInOut {
  id               String           @id @default(cuid())
  attendanceId     String
  employeeId       String
  type             CheckType        // CHECK_IN, CHECK_OUT, BREAK_START, BREAK_END
  timestamp        DateTime
  location         Json?            // GPS coordinates with accuracy
  method           AttendanceMethod
  deviceInfo       Json?            // Device/biometric info
  ipAddress        String?
  isManualEntry    Boolean          @default(false)
  manualReason     String?
  approvedBy       String?
  createdAt        DateTime         @default(now())

  attendance AttendanceRecord @relation(fields: [attendanceId], references: [id], onDelete: Cascade)

  @@index([employeeId, timestamp])
  @@index([type])
  @@map("check_in_out")
}

// Location management for geo-fencing
model Location {
  id          String   @id @default(cuid())
  name        String   @unique
  address     String?
  latitude    Decimal  @db.Decimal(10, 8)
  longitude   Decimal  @db.Decimal(11, 8)
  radius      Int      // Radius in meters for geo-fencing
  isActive    Boolean  @default(true)
  timezone    String   @default("Asia/Kolkata")
  workingHours Json?   // Working hours configuration
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  employeeLocations EmployeeLocation[]

  @@index([latitude, longitude])
  @@map("locations")
}

// Office Location Management for predefined locations
model OfficeLocation {
  id          String   @id @default(cuid())
  name        String   @unique
  code        String   @unique
  address     String
  city        String
  state       String
  country     String   @default("India")
  latitude    Decimal  @db.Decimal(10, 8)
  longitude   Decimal  @db.Decimal(11, 8)
  radius      Int      @default(100) // Default radius in meters
  timezone    String   @default("Asia/Kolkata")
  workingHours Json?   // Working hours configuration
  isHeadOffice Boolean @default(false)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  employeeLocations EmployeeLocation[]

  @@index([city, state])
  @@index([latitude, longitude])
  @@map("office_locations")
}

// Site Master Data for Field Employees
model Site {
  id          String   @id @default(cuid())
  name        String   @unique
  code        String   @unique
  address     String
  city        String
  state       String
  country     String   @default("India")
  latitude    Decimal  @db.Decimal(10, 8)
  longitude   Decimal  @db.Decimal(11, 8)
  radius      Int      @default(50) // Radius in meters for check-in validation
  contactPerson String?
  contactPhone  String?
  contactEmail  String?
  description   String?
  siteType      SiteType @default(CLIENT)
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  siteVisits    SiteVisit[]
  employeeSites EmployeeSite[]

  @@index([city, state])
  @@index([latitude, longitude])
  @@index([siteType])
  @@map("sites")
}

// Employee Site Assignment for Field Employees
model EmployeeSite {
  id         String   @id @default(cuid())
  employeeId String
  siteId     String
  assignedBy String   // User ID who assigned this site
  assignedAt DateTime @default(now())
  isActive   Boolean  @default(true)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  employee Employee @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  site     Site     @relation(fields: [siteId], references: [id], onDelete: Cascade)

  @@unique([employeeId, siteId])
  @@index([employeeId])
  @@index([siteId])
  @@map("employee_sites")
}

// Site Visit Tracking for Field Employees
model SiteVisit {
  id               String           @id @default(cuid())
  employeeId       String
  siteId           String
  date             DateTime         @db.Date
  checkInTime      DateTime
  checkOutTime     DateTime?
  checkInLocation  Json             // GPS coordinates at check-in
  checkOutLocation Json?            // GPS coordinates at check-out
  purpose          String?          // Purpose of visit
  notes            String?          // Visit notes
  photos           Json?            // Array of photo URLs
  status           SiteVisitStatus  @default(IN_PROGRESS)
  distanceFromSite Decimal?         @db.Decimal(8, 2) // Distance in meters
  duration         Int?             // Duration in minutes
  isValidLocation  Boolean          @default(true)    // Whether check-in was within site radius
  createdAt        DateTime         @default(now())
  updatedAt        DateTime         @updatedAt

  employee Employee @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  site     Site     @relation(fields: [siteId], references: [id], onDelete: Cascade)

  @@index([employeeId, date])
  @@index([siteId, date])
  @@index([status])
  @@map("site_visits")
}

// Employee Location Assignment for geo-fencing
model EmployeeLocation {
  id               String   @id @default(cuid())
  employeeId       String
  name             String   // Custom name for this location
  latitude         Decimal  @db.Decimal(10, 8)
  longitude        Decimal  @db.Decimal(11, 8)
  radius           Int      @default(100) // Radius in meters
  isOfficeLocation Boolean  @default(false)
  officeLocationId String?  // Reference to predefined office location
  locationId       String?  // Reference to custom location (for backward compatibility)
  isActive         Boolean  @default(true)
  assignedBy       String   // User ID who assigned this location
  assignedAt       DateTime @default(now())
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  employee       Employee       @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  officeLocation OfficeLocation? @relation(fields: [officeLocationId], references: [id])
  location       Location?      @relation(fields: [locationId], references: [id], onDelete: Cascade)

  @@index([employeeId])
  @@index([officeLocationId])
  @@map("employee_locations")
}

// Attendance requests for out-of-location check-ins
model AttendanceRequest {
  id          String                 @id @default(cuid())
  employeeId  String
  date        DateTime               @db.Date
  checkInTime DateTime
  location    Json?                  // GPS coordinates where employee tried to check in
  reason      String                 // Reason for out-of-location check-in
  status      AttendanceRequestStatus @default(PENDING)
  approvedBy  String?
  approvedAt  DateTime?
  rejectedAt  DateTime?
  rejectionReason String?
  createdAt   DateTime               @default(now())
  updatedAt   DateTime               @updatedAt

  employee Employee @relation(fields: [employeeId], references: [id], onDelete: Cascade)

  @@index([employeeId, status])
  @@index([date])
  @@map("attendance_requests")
}

// Attendance policies and rules
model AttendancePolicy {
  id                    String   @id @default(cuid())
  name                  String   @unique
  description           String?
  workingHoursPerDay    Decimal  @db.Decimal(4, 2) @default(8.00)
  workingDaysPerWeek    Int      @default(5)
  graceTimeMinutes      Int      @default(15)
  halfDayThresholdHours Decimal  @db.Decimal(4, 2) @default(4.00)
  overtimeThresholdHours Decimal @db.Decimal(4, 2) @default(8.00)
  allowFlexiTime        Boolean  @default(false)
  requireGeoFencing     Boolean  @default(false)
  isActive              Boolean  @default(true)
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  @@map("attendance_policies")
}

// Timesheet Management System
model Project {
  id          String   @id @default(cuid())
  name        String   @unique
  code        String   @unique
  description String?
  clientName  String?
  startDate   DateTime @db.Date
  endDate     DateTime? @db.Date
  status      ProjectStatus @default(ACTIVE)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  timeEntries TimeEntry[]

  @@index([status])
  @@map("projects")
}

model Timesheet {
  id         String          @id @default(cuid())
  employeeId String
  startDate  DateTime        @db.Date
  endDate    DateTime        @db.Date
  status     TimesheetStatus @default(DRAFT)
  totalHours Decimal         @db.Decimal(6, 2) @default(0)
  approvedBy String?
  approvedAt DateTime?
  rejectedAt DateTime?
  rejectionReason String?
  submittedAt DateTime?
  createdAt  DateTime        @default(now())
  updatedAt  DateTime        @updatedAt

  employee Employee   @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  approver User?      @relation("TimesheetApprover", fields: [approvedBy], references: [id])
  entries  TimeEntry[]

  @@unique([employeeId, startDate, endDate])
  @@index([employeeId, status])
  @@index([startDate, endDate])
  @@map("timesheets")
}

model TimeEntry {
  id            String   @id @default(cuid())
  timesheetId   String
  employeeId    String
  date          DateTime @db.Date
  startTime     String   // HH:MM format
  endTime       String   // HH:MM format
  breakDuration Int      @default(0) // Break duration in minutes
  projectId     String?
  taskDescription String?
  billableHours Decimal  @db.Decimal(4, 2)
  nonBillableHours Decimal @db.Decimal(4, 2) @default(0)
  overtimeHours Decimal  @db.Decimal(4, 2) @default(0)
  isApproved    Boolean  @default(false)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  timesheet Timesheet @relation(fields: [timesheetId], references: [id], onDelete: Cascade)
  project   Project?  @relation(fields: [projectId], references: [id])

  @@unique([timesheetId, date])
  @@index([employeeId, date])
  @@index([projectId])
  @@map("time_entries")
}

// Leave Management
model LeavePolicy {
  id                    String   @id @default(cuid())
  name                  String   @unique
  type                  LeaveType
  code                  String   @unique
  description           String?
  daysPerYear           Int
  carryForward          Boolean  @default(false)
  maxCarryForward       Int?
  maxConsecutiveDays    Int?     // Maximum consecutive days allowed
  minAdvanceNotice      Int?     // Minimum days of advance notice required
  requiresApproval      Boolean  @default(true)
  approvalLevels        Int      @default(1) // Number of approval levels required
  accrualType           AccrualType @default(ANNUAL) // How leave is accrued
  accrualRate           Decimal? @db.Decimal(4, 2) // Days per month for monthly accrual
  probationPeriodDays   Int?     // Days after joining when leave becomes available
  gender                Gender?  // Gender-specific policies (e.g., maternity)
  isEncashable          Boolean  @default(false) // Can unused leave be encashed
  encashmentRate        Decimal? @db.Decimal(5, 2) // Percentage of salary for encashment
  isActive              Boolean  @default(true)
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  leaveRequests LeaveRequest[]
  leaveBalances LeaveBalance[]

  @@map("leave_policies")
}

model LeaveBalance {
  id              String   @id @default(cuid())
  employeeId      String
  policyId        String
  year            Int      // Calendar year
  allocated       Decimal  @db.Decimal(4, 2) // Total allocated for the year
  used            Decimal  @db.Decimal(4, 2) @default(0) // Used leave days
  pending         Decimal  @db.Decimal(4, 2) @default(0) // Pending approval
  carriedForward  Decimal  @db.Decimal(4, 2) @default(0) // Carried from previous year
  encashed        Decimal  @db.Decimal(4, 2) @default(0) // Encashed leave days
  expired         Decimal  @db.Decimal(4, 2) @default(0) // Expired leave days
  available       Decimal  @db.Decimal(4, 2) // Available balance (computed)
  lastAccrualDate DateTime? // Last date when leave was accrued
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  employee Employee    @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  policy   LeavePolicy @relation(fields: [policyId], references: [id])

  @@unique([employeeId, policyId, year])
  @@index([employeeId, year])
  @@map("leave_balances")
}

model LeaveRequest {
  id              String            @id @default(cuid())
  employeeId      String
  policyId        String
  startDate       DateTime          @db.Date
  endDate         DateTime          @db.Date
  days            Decimal           @db.Decimal(4, 2)
  reason          String
  emergencyContact Json?            // Emergency contact details for long leaves
  handoverNotes   String?          // Work handover notes
  attachments     Json?            // Supporting documents
  status          LeaveRequestStatus @default(PENDING)
  appliedAt       DateTime          @default(now())
  approvedAt      DateTime?
  approvedBy      String?
  rejectedAt      DateTime?
  rejectionReason String?
  cancelledAt     DateTime?
  cancellationReason String?
  isHalfDay       Boolean           @default(false)
  halfDayType     HalfDayType?      // FIRST_HALF or SECOND_HALF
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  employee Employee    @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  policy   LeavePolicy @relation(fields: [policyId], references: [id])
  approvals LeaveApproval[]

  @@index([employeeId, status])
  @@index([startDate, endDate])
  @@map("leave_requests")
}

model LeaveApproval {
  id              String         @id @default(cuid())
  leaveRequestId  String
  approverId      String
  approverName    String?        // Store approver name for historical reference
  approverEmail   String?        // Store approver email for historical reference
  level           Int            // Approval level (1, 2, 3, etc.)
  status          ApprovalStatus @default(PENDING)
  approvedAt      DateTime?
  rejectedAt      DateTime?
  comments        String?
  notificationSent Boolean       @default(false)
  notificationSentAt DateTime?
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt

  leaveRequest    LeaveRequest   @relation(fields: [leaveRequestId], references: [id], onDelete: Cascade)

  @@unique([leaveRequestId, level])
  @@index([approverId, status])
  @@map("leave_approvals")
}

// Payroll Management

// Salary Grade and Band Management
model SalaryGrade {
  id          String   @id @default(cuid())
  name        String   @unique // e.g., "L1", "L2", "Manager", "Senior Manager"
  code        String   @unique // e.g., "L1", "L2", "MGR", "SMGR"
  description String?
  minSalary   Decimal  @db.Decimal(12, 2)
  maxSalary   Decimal  @db.Decimal(12, 2)
  currency    String   @default("INR")
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  salaryStructures SalaryStructure[]

  @@map("salary_grades")
}

// Configurable Pay Components
model PayComponent {
  id              String            @id @default(cuid())
  name            String            @unique // e.g., "Basic Salary", "HRA", "Transport Allowance"
  code            String            @unique // e.g., "BASIC", "HRA", "TRANSPORT"
  type            PayComponentType  // EARNING, DEDUCTION
  category        PayComponentCategory // BASIC, ALLOWANCE, STATUTORY_DEDUCTION, OTHER_DEDUCTION
  calculationType CalculationType   // FIXED, PERCENTAGE, FORMULA
  isStatutory     Boolean           @default(false) // For PF, ESI, PT, TDS
  isTaxable       Boolean           @default(true)
  isActive        Boolean           @default(true)
  description     String?
  formula         String?           // For complex calculations
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  structureComponents SalaryStructureComponent[]

  @@map("pay_components")
}

// Salary Structure Template
model SalaryStructure {
  id          String   @id @default(cuid())
  name        String   @unique // e.g., "Software Engineer L1", "Manager Grade"
  code        String   @unique
  gradeId     String?
  description String?
  isActive    Boolean  @default(true)
  effectiveFrom DateTime @default(now())
  effectiveTo   DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  grade       SalaryGrade? @relation(fields: [gradeId], references: [id])
  components  SalaryStructureComponent[]
  employeeSalaries EmployeeSalaryStructure[]

  @@map("salary_structures")
}

// Components within a salary structure
model SalaryStructureComponent {
  id                String            @id @default(cuid())
  structureId       String
  componentId       String
  value             Decimal?          @db.Decimal(12, 2) // Fixed amount
  percentage        Decimal?          @db.Decimal(5, 2)  // Percentage of basic/gross
  baseComponent     String?           // Component code to calculate percentage from
  minValue          Decimal?          @db.Decimal(12, 2)
  maxValue          Decimal?          @db.Decimal(12, 2)
  isVariable        Boolean           @default(false)
  order             Int               @default(0) // Display order
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  structure   SalaryStructure @relation(fields: [structureId], references: [id], onDelete: Cascade)
  component   PayComponent    @relation(fields: [componentId], references: [id])

  @@unique([structureId, componentId])
  @@map("salary_structure_components")
}

// Employee-specific salary assignments
model EmployeeSalaryStructure {
  id              String   @id @default(cuid())
  employeeId      String
  structureId     String
  ctc             Decimal  @db.Decimal(12, 2) // Cost to Company
  effectiveFrom   DateTime @default(now())
  effectiveTo     DateTime?
  revisionReason  String?  // Increment, Promotion, Market Correction, etc.
  approvedBy      String?
  approvedAt      DateTime?
  isActive        Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  employee    Employee        @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  structure   SalaryStructure @relation(fields: [structureId], references: [id])
  components  EmployeeSalaryComponent[]

  @@index([employeeId, effectiveFrom])
  @@map("employee_salary_structures")
}

// Employee-specific component values (overrides)
model EmployeeSalaryComponent {
  id                    String   @id @default(cuid())
  employeeSalaryId      String
  componentId           String
  value                 Decimal  @db.Decimal(12, 2)
  isOverride            Boolean  @default(false) // True if manually overridden
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  employeeSalary  EmployeeSalaryStructure @relation(fields: [employeeSalaryId], references: [id], onDelete: Cascade)

  @@unique([employeeSalaryId, componentId])
  @@map("employee_salary_components")
}

// Salary Revision History
model SalaryRevision {
  id              String          @id @default(cuid())
  employeeId      String
  revisionType    RevisionType    // INCREMENT, PROMOTION, MARKET_CORRECTION, BONUS
  oldCTC          Decimal         @db.Decimal(12, 2)
  newCTC          Decimal         @db.Decimal(12, 2)
  incrementAmount Decimal         @db.Decimal(12, 2)
  incrementPercent Decimal        @db.Decimal(5, 2)
  effectiveFrom   DateTime
  reason          String?
  approvedBy      String?
  approvedAt      DateTime?
  status          RevisionStatus  @default(PENDING)
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt

  employee Employee @relation(fields: [employeeId], references: [id], onDelete: Cascade)

  @@index([employeeId, effectiveFrom])
  @@map("salary_revisions")
}

model PayrollRun {
  id          String         @id @default(cuid())
  period      String         // YYYY-MM format
  startDate   DateTime       @db.Date
  endDate     DateTime       @db.Date
  status      PayrollStatus  @default(DRAFT)
  processedAt DateTime?
  processedBy String?
  totalGross  Decimal?       @db.Decimal(15, 2)
  totalNet    Decimal?       @db.Decimal(15, 2)
  totalDeductions Decimal?   @db.Decimal(15, 2)
  employeeCount Int?
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt

  payrollRecords PayrollRecord[]
  payslips       Payslip[]

  @@unique([period])
  @@map("payroll_runs")
}

model PayrollRecord {
  id              String     @id @default(cuid())
  employeeId      String
  payrollRunId    String
  basicSalary     Decimal    @db.Decimal(12, 2)
  grossSalary     Decimal    @db.Decimal(12, 2)
  netSalary       Decimal    @db.Decimal(12, 2)
  totalEarnings   Decimal    @db.Decimal(12, 2)
  totalDeductions Decimal    @db.Decimal(12, 2)
  
  // Statutory Deductions
  pfDeduction     Decimal?   @db.Decimal(10, 2)
  esiDeduction    Decimal?   @db.Decimal(10, 2)
  tdsDeduction    Decimal?   @db.Decimal(10, 2)
  ptDeduction     Decimal?   @db.Decimal(10, 2)
  
  // Attendance-based calculations
  workingDays     Int
  presentDays     Decimal    @db.Decimal(4, 2)
  absentDays      Decimal    @db.Decimal(4, 2)
  overtimeHours   Decimal?   @db.Decimal(6, 2)
  overtimeAmount  Decimal?   @db.Decimal(10, 2)
  
  // Leave deductions
  lopDays         Decimal?   @db.Decimal(4, 2) // Loss of Pay days
  lopAmount       Decimal?   @db.Decimal(10, 2)
  
  // Component-wise breakdown
  earnings        Json       // Array of earning components with amounts
  deductions      Json       // Array of deduction components with amounts
  
  status          PayrollRecordStatus @default(DRAFT)
  approvedBy      String?
  approvedAt      DateTime?
  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @updatedAt

  employee   Employee   @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  payrollRun PayrollRun @relation(fields: [payrollRunId], references: [id], onDelete: Cascade)

  @@unique([employeeId, payrollRunId])
  @@index([payrollRunId, status])
  @@map("payroll_records")
}

// Payslip Management
model Payslip {
  id            String        @id @default(cuid())
  employeeId    String
  payrollRunId  String
  fileName      String
  fileSize      Int?
  generatedAt   DateTime      @default(now())
  generatedBy   String
  accessedAt    DateTime?
  downloadCount Int           @default(0)
  emailSent     Boolean       @default(false)
  emailSentAt   DateTime?
  status        PayslipStatus @default(GENERATED)
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  employee     Employee    @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  payrollRun   PayrollRun  @relation(fields: [payrollRunId], references: [id], onDelete: Cascade)

  @@unique([employeeId, payrollRunId])
  @@index([employeeId])
  @@index([payrollRunId])
  @@map("payslips")
}

// Expense and Travel Management

// Expense Categories Configuration
model ExpenseCategory {
  id          String   @id @default(cuid())
  name        String   @unique
  code        String   @unique
  description String?
  maxAmount   Decimal? @db.Decimal(12, 2) // Maximum allowed amount per claim
  requiresReceipt Boolean @default(true)
  requiresApproval Boolean @default(true)
  approvalLevels Int    @default(1)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  expenseClaims ExpenseClaim[]
  policyRules   ExpensePolicyRule[]

  @@map("expense_categories")
}

// Expense Policy Rules
model ExpensePolicyRule {
  id          String   @id @default(cuid())
  categoryId  String
  name        String
  description String?
  ruleType    PolicyRuleType // AMOUNT_LIMIT, FREQUENCY_LIMIT, APPROVAL_REQUIRED
  ruleValue   Json     // Flexible rule configuration
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  category ExpenseCategory @relation(fields: [categoryId], references: [id], onDelete: Cascade)

  @@map("expense_policy_rules")
}

// Enhanced Expense Claims
model ExpenseClaim {
  id              String            @id @default(cuid())
  employeeId      String
  categoryId      String
  title           String
  description     String?
  amount          Decimal           @db.Decimal(12, 2)
  currency        String            @default("INR")
  expenseDate     DateTime          @db.Date
  location        Json?             // GPS coordinates where expense occurred
  merchantName    String?           // Vendor/merchant name
  merchantAddress String?           // Vendor address
  billNumber      String?           // Bill/invoice number
  taxAmount       Decimal?          @db.Decimal(10, 2)
  taxRate         Decimal?          @db.Decimal(5, 2)
  isReimbursable  Boolean           @default(true)
  isPetrolExpense Boolean           @default(false) // Auto-generated petrol expenses
  distanceTraveled Decimal?         @db.Decimal(8, 2) // For fuel/travel expenses
  vehicleNumber   String?           // Vehicle used for travel
  status          ExpenseStatus     @default(PENDING)
  submittedAt     DateTime          @default(now())
  approvedAt      DateTime?
  approvedBy      String?
  rejectedAt      DateTime?
  rejectionReason String?
  reimbursedAt    DateTime?
  reimbursedBy    String?
  reimbursementAmount Decimal?      @db.Decimal(12, 2)
  reimbursementBatchId String?      // Reference to reimbursement batch
  policyViolations Json?            // Array of policy violations
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  employee    Employee              @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  category    ExpenseCategory       @relation(fields: [categoryId], references: [id])
  attachments ExpenseAttachment[]
  approvals   ExpenseApproval[]
  travelRequest TravelRequest?      @relation(fields: [travelRequestId], references: [id])
  travelRequestId String?
  monthlyPetrolExpense MonthlyPetrolExpense?
  reimbursementBatch ReimbursementBatch? @relation(fields: [reimbursementBatchId], references: [id])

  @@index([employeeId, status])
  @@index([expenseDate])
  @@index([categoryId])
  @@map("expense_claims")
}

// Expense Attachments/Receipts
model ExpenseAttachment {
  id            String   @id @default(cuid())
  expenseId     String
  fileName      String
  originalName  String
  fileSize      Int
  mimeType      String
  fileUrl       String
  thumbnailUrl  String?
  uploadedAt    DateTime @default(now())
  createdAt     DateTime @default(now())

  expense ExpenseClaim @relation(fields: [expenseId], references: [id], onDelete: Cascade)

  @@map("expense_attachments")
}

// Expense Approval Workflow
model ExpenseApproval {
  id              String         @id @default(cuid())
  expenseId       String
  approverId      String
  approverName    String?
  approverEmail   String?
  level           Int            // Approval level (1, 2, 3, etc.)
  status          ApprovalStatus @default(PENDING)
  approvedAt      DateTime?
  rejectedAt      DateTime?
  comments        String?
  notificationSent Boolean       @default(false)
  notificationSentAt DateTime?
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt

  expense ExpenseClaim @relation(fields: [expenseId], references: [id], onDelete: Cascade)

  @@unique([expenseId, level])
  @@index([approverId, status])
  @@map("expense_approvals")
}

// Reimbursement Batch Processing
model ReimbursementBatch {
  id              String            @id @default(cuid())
  batchId         String            @unique
  totalAmount     Decimal           @db.Decimal(15, 2)
  totalClaims     Int
  paymentMethod   PaymentMethod     @default(BANK_TRANSFER)
  referenceNumber String?           // Bank reference or cheque number
  notes           String?
  status          ReimbursementStatus @default(PROCESSING)
  processedBy     String
  processedAt     DateTime
  completedAt     DateTime?
  failedAt        DateTime?
  failureReason   String?
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  expenseClaims   ExpenseClaim[]

  @@index([status])
  @@index([processedAt])
  @@map("reimbursement_batches")
}

// Travel Requests
model TravelRequest {
  id              String            @id @default(cuid())
  employeeId      String
  title           String
  purpose         String
  destination     String
  fromLocation    String
  startDate       DateTime          @db.Date
  endDate         DateTime          @db.Date
  estimatedCost   Decimal           @db.Decimal(12, 2)
  actualCost      Decimal?          @db.Decimal(12, 2)
  travelMode      TravelMode        @default(FLIGHT)
  accommodationRequired Boolean     @default(false)
  advanceRequired Boolean           @default(false)
  advanceAmount   Decimal?          @db.Decimal(12, 2)
  status          TravelRequestStatus @default(PENDING)
  itinerary       Json?             // Detailed travel itinerary
  approvedAt      DateTime?
  approvedBy      String?
  rejectedAt      DateTime?
  rejectionReason String?
  completedAt     DateTime?
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  employee      Employee          @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  expenseClaims ExpenseClaim[]
  approvals     TravelApproval[]

  @@index([employeeId, status])
  @@index([startDate, endDate])
  @@map("travel_requests")
}

// Travel Approval Workflow
model TravelApproval {
  id              String         @id @default(cuid())
  travelRequestId String
  approverId      String
  approverName    String?
  approverEmail   String?
  level           Int            // Approval level (1, 2, 3, etc.)
  status          ApprovalStatus @default(PENDING)
  approvedAt      DateTime?
  rejectedAt      DateTime?
  comments        String?
  notificationSent Boolean       @default(false)
  notificationSentAt DateTime?
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt

  travelRequest TravelRequest @relation(fields: [travelRequestId], references: [id], onDelete: Cascade)

  @@unique([travelRequestId, level])
  @@index([approverId, status])
  @@map("travel_approvals")
}

// Petrol Expense Configuration
model PetrolExpenseConfig {
  id          String   @id @default(cuid())
  ratePerKm   Decimal  @db.Decimal(6, 2) // Rate per kilometer
  currency    String   @default("INR")
  effectiveFrom DateTime @default(now())
  effectiveTo   DateTime?
  isActive    Boolean  @default(true)
  createdBy   String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("petrol_expense_config")
}

// Monthly Petrol Expense Summary
model MonthlyPetrolExpense {
  id              String   @id @default(cuid())
  employeeId      String
  month           Int      // 1-12
  year            Int
  totalDistance   Decimal  @db.Decimal(10, 2) // Total distance in km
  totalAmount     Decimal  @db.Decimal(12, 2) // Total petrol expense
  ratePerKm       Decimal  @db.Decimal(6, 2)  // Rate used for calculation
  status          ExpenseStatus @default(PENDING)
  autoGenerated   Boolean  @default(true)
  expenseClaimId  String?  @unique // Reference to auto-generated expense claim
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  employee     Employee      @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  expenseClaim ExpenseClaim? @relation(fields: [expenseClaimId], references: [id])

  @@unique([employeeId, month, year])
  @@index([employeeId, year])
  @@map("monthly_petrol_expenses")
}

// Performance Management
model PerformanceReview {
  id           String           @id @default(cuid())
  employeeId   String
  reviewerId   String
  cycleId      String?          // Reference to review cycle
  period       String           // YYYY-QX or YYYY format
  type         ReviewType       @default(ANNUAL)
  status       ReviewStatus     @default(DRAFT)
  selfRating   Json?            // Self assessment scores
  managerRating Json?           // Manager assessment scores
  peerRating   Json?            // Peer assessment scores (360 feedback)
  subordinateRating Json?       // Subordinate assessment scores (360 feedback)
  goals        Json?            // OKRs and goals
  achievements Json?            // Key achievements during the period
  developmentAreas Json?        // Areas for improvement
  feedback     String?          // Overall feedback comments
  overallRating Decimal?        @db.Decimal(3, 2) // Overall rating (1.00 to 5.00)
  calibrationRating Decimal?    @db.Decimal(3, 2) // Post-calibration rating
  isCalibrated Boolean          @default(false)
  calibratedBy String?
  calibratedAt DateTime?
  submittedAt  DateTime?
  completedAt  DateTime?
  dueDate      DateTime?
  createdAt    DateTime         @default(now())
  updatedAt    DateTime         @updatedAt

  employee     Employee          @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  cycle        PerformanceCycle? @relation(fields: [cycleId], references: [id])
  objectives   Objective[]
  feedbacks    Feedback[]

  @@index([employeeId, period])
  @@index([cycleId])
  @@index([status])
  @@map("performance_reviews")
}

// Performance Review Cycles
model PerformanceCycle {
  id          String           @id @default(cuid())
  name        String           @unique // e.g., "Q1 2024 Review", "Annual Review 2024"
  description String?
  type        ReviewType       @default(ANNUAL)
  startDate   DateTime         @db.Date
  endDate     DateTime         @db.Date
  dueDate     DateTime         @db.Date // When reviews should be completed
  status      CycleStatus      @default(DRAFT)
  isActive    Boolean          @default(true)
  template    Json?            // Review template configuration
  createdBy   String
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt

  reviews     PerformanceReview[]
  objectives  Objective[]

  @@index([status, isActive])
  @@index([startDate, endDate])
  @@map("performance_cycles")
}

// OKR (Objectives and Key Results) Management
model Objective {
  id              String           @id @default(cuid())
  employeeId      String
  reviewId        String?          // Link to performance review
  cycleId         String?          // Link to performance cycle
  title           String
  description     String?
  category        ObjectiveCategory @default(INDIVIDUAL)
  priority        Priority         @default(MEDIUM)
  weight          Decimal          @db.Decimal(5, 2) @default(25.00) // Percentage weight (0-100)
  status          ObjectiveStatus  @default(ACTIVE)
  progress        Decimal          @db.Decimal(5, 2) @default(0.00) // Progress percentage (0-100)
  startDate       DateTime         @db.Date
  endDate         DateTime         @db.Date
  parentId        String?          // For cascaded objectives
  alignedTo       String?          // Department/company objective alignment
  createdBy       String
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt

  employee        Employee         @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  review          PerformanceReview? @relation(fields: [reviewId], references: [id])
  cycle           PerformanceCycle? @relation(fields: [cycleId], references: [id])
  parent          Objective?       @relation("ObjectiveHierarchy", fields: [parentId], references: [id])
  children        Objective[]      @relation("ObjectiveHierarchy")
  keyResults      KeyResult[]
  updates         ObjectiveUpdate[]

  @@index([employeeId, status])
  @@index([cycleId])
  @@index([parentId])
  @@map("objectives")
}

// Key Results for OKRs
model KeyResult {
  id              String           @id @default(cuid())
  objectiveId     String
  title           String
  description     String?
  type            KeyResultType    @default(QUANTITATIVE)
  targetValue     Decimal?         @db.Decimal(15, 2) // Target numeric value
  currentValue    Decimal?         @db.Decimal(15, 2) // Current numeric value
  unit            String?          // Unit of measurement (%, $, count, etc.)
  targetDate      DateTime?        @db.Date
  status          KeyResultStatus  @default(ACTIVE)
  progress        Decimal          @db.Decimal(5, 2) @default(0.00) // Progress percentage
  weight          Decimal          @db.Decimal(5, 2) @default(25.00) // Weight within objective
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt

  objective       Objective        @relation(fields: [objectiveId], references: [id], onDelete: Cascade)
  updates         KeyResultUpdate[]

  @@index([objectiveId])
  @@map("key_results")
}

// Progress Updates for Objectives
model ObjectiveUpdate {
  id              String           @id @default(cuid())
  objectiveId     String
  progress        Decimal          @db.Decimal(5, 2) // Progress percentage
  comments        String?
  challenges      String?          // Challenges faced
  nextSteps       String?          // Next steps planned
  updatedBy       String
  updateDate      DateTime         @db.Date
  createdAt       DateTime         @default(now())

  objective       Objective        @relation(fields: [objectiveId], references: [id], onDelete: Cascade)

  @@index([objectiveId, updateDate])
  @@map("objective_updates")
}

// Progress Updates for Key Results
model KeyResultUpdate {
  id              String           @id @default(cuid())
  keyResultId     String
  currentValue    Decimal          @db.Decimal(15, 2)
  progress        Decimal          @db.Decimal(5, 2) // Progress percentage
  comments        String?
  evidence        Json?            // Supporting evidence/links
  updatedBy       String
  updateDate      DateTime         @db.Date
  createdAt       DateTime         @default(now())

  keyResult       KeyResult        @relation(fields: [keyResultId], references: [id], onDelete: Cascade)

  @@index([keyResultId, updateDate])
  @@map("key_result_updates")
}

// 360-Degree Feedback System
model Feedback {
  id              String           @id @default(cuid())
  reviewId        String?          // Link to performance review
  employeeId      String           // Employee being reviewed
  reviewerId      String           // Person giving feedback
  reviewerType    ReviewerType     // SELF, MANAGER, PEER, SUBORDINATE, EXTERNAL
  relationship    String?          // Description of working relationship
  isAnonymous     Boolean          @default(false)
  status          FeedbackStatus   @default(PENDING)
  responses       Json?            // Feedback responses to questions
  overallRating   Decimal?         @db.Decimal(3, 2) // Overall rating
  strengths       String?          // Key strengths
  improvements    String?          // Areas for improvement
  comments        String?          // Additional comments
  submittedAt     DateTime?
  dueDate         DateTime?        @db.Date
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt

  employee        Employee         @relation("FeedbackEmployee", fields: [employeeId], references: [id], onDelete: Cascade)
  reviewer        Employee         @relation("FeedbackReviewer", fields: [reviewerId], references: [id], onDelete: Cascade)
  review          PerformanceReview? @relation(fields: [reviewId], references: [id])

  @@index([employeeId, reviewerType])
  @@index([reviewerId])
  @@index([reviewId])
  @@map("feedbacks")
}

// Feedback Templates and Questions
model FeedbackTemplate {
  id              String           @id @default(cuid())
  name            String           @unique
  description     String?
  reviewerType    ReviewerType     // Which type of reviewer this template is for
  isActive        Boolean          @default(true)
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt

  questions       FeedbackQuestion[]

  @@map("feedback_templates")
}

model FeedbackQuestion {
  id              String           @id @default(cuid())
  templateId      String
  question        String
  type            QuestionType     @default(RATING) // RATING, TEXT, MULTIPLE_CHOICE
  options         Json?            // For multiple choice questions
  isRequired      Boolean          @default(true)
  order           Int              @default(0)
  category        String?          // Question category (Leadership, Communication, etc.)
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt

  template        FeedbackTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)

  @@index([templateId, order])
  @@map("feedback_questions")
}

// Document Management
model Document {
  id          String         @id @default(cuid())
  employeeId  String?
  title       String
  description String?
  category    DocumentCategory
  fileName    String
  fileUrl     String
  fileSize    Int?
  mimeType    String?
  version     Int            @default(1)
  isActive    Boolean        @default(true)
  expiryDate  DateTime?
  uploadedBy  String
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt

  employee Employee? @relation(fields: [employeeId], references: [id], onDelete: Cascade)

  @@map("documents")
}

// Employee Onboarding System
model OnboardingTemplate {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  tasks       OnboardingTask[]
  workflows   OnboardingWorkflow[]

  @@map("onboarding_templates")
}

model OnboardingTask {
  id          String   @id @default(cuid())
  templateId  String
  title       String
  description String?
  category    OnboardingCategory
  isRequired  Boolean  @default(true)
  order       Int
  daysToComplete Int?   // Days from joining date
  assignedRole UserRole @default(EMPLOYEE) // Who should complete this task
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  template    OnboardingTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)
  workflowTasks OnboardingWorkflowTask[]

  @@map("onboarding_tasks")
}

model OnboardingWorkflow {
  id          String            @id @default(cuid())
  employeeId  String            @unique
  templateId  String
  status      OnboardingStatus  @default(PENDING)
  startDate   DateTime          @default(now())
  dueDate     DateTime?
  completedAt DateTime?
  assignedTo  String?           // HR person responsible
  notes       String?
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt

  employee    Employee          @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  template    OnboardingTemplate @relation(fields: [templateId], references: [id])
  tasks       OnboardingWorkflowTask[]
  approvals   OnboardingApproval[]

  @@map("onboarding_workflows")
}

model OnboardingWorkflowTask {
  id          String            @id @default(cuid())
  workflowId  String
  taskId      String
  status      TaskStatus        @default(PENDING)
  assignedTo  String?
  startedAt   DateTime?
  completedAt DateTime?
  dueDate     DateTime?
  notes       String?
  documents   Json?             // Array of uploaded document URLs
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt

  workflow    OnboardingWorkflow @relation(fields: [workflowId], references: [id], onDelete: Cascade)
  task        OnboardingTask     @relation(fields: [taskId], references: [id])

  @@unique([workflowId, taskId])
  @@map("onboarding_workflow_tasks")
}

model OnboardingApproval {
  id          String            @id @default(cuid())
  workflowId  String
  approverRole UserRole
  approvedBy  String?
  status      ApprovalStatus    @default(PENDING)
  approvedAt  DateTime?
  rejectedAt  DateTime?
  comments    String?
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt

  workflow    OnboardingWorkflow @relation(fields: [workflowId], references: [id], onDelete: Cascade)

  @@map("onboarding_approvals")
}

// Enums

enum Gender {
  MALE
  FEMALE
  OTHER
}

enum EmploymentType {
  FULL_TIME
  PART_TIME
  CONTRACT
  INTERN
}

enum EmployeeType {
  NORMAL
  FIELD_EMPLOYEE
}

enum EmployeeStatus {
  ACTIVE
  INACTIVE
  TERMINATED
  ON_LEAVE
}

enum AttendanceMethod {
  BIOMETRIC
  GPS
  WEB
  MOBILE
}

enum AttendanceStatus {
  PRESENT
  ABSENT
  LATE
  HALF_DAY
  WORK_FROM_HOME
  ON_LEAVE
  HOLIDAY
}

enum CheckType {
  CHECK_IN
  CHECK_OUT
  BREAK_START
  BREAK_END
}

enum LeaveType {
  ANNUAL
  SICK
  CASUAL
  MATERNITY
  PATERNITY
  EMERGENCY
  COMPENSATORY
}

enum LeaveRequestStatus {
  PENDING
  APPROVED
  REJECTED
  CANCELLED
  PARTIALLY_APPROVED
}

enum AccrualType {
  ANNUAL      // All days allocated at start of year
  MONTHLY     // Days accrued monthly
  QUARTERLY   // Days accrued quarterly
  ON_JOINING  // Days allocated on joining date
}

enum HalfDayType {
  FIRST_HALF
  SECOND_HALF
}

enum PayrollStatus {
  DRAFT
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
}

enum PayrollRecordStatus {
  DRAFT
  CALCULATED
  APPROVED
  PAID
  CANCELLED
}

enum PayComponentType {
  EARNING
  DEDUCTION
}

enum PayComponentCategory {
  BASIC
  ALLOWANCE
  BONUS
  OVERTIME
  STATUTORY_DEDUCTION
  OTHER_DEDUCTION
  REIMBURSEMENT
}

enum CalculationType {
  FIXED
  PERCENTAGE
  FORMULA
  ATTENDANCE_BASED
}

enum RevisionType {
  INCREMENT
  PROMOTION
  MARKET_CORRECTION
  BONUS
  DEMOTION
  SALARY_CUT
}

enum RevisionStatus {
  PENDING
  APPROVED
  REJECTED
  IMPLEMENTED
}

enum ExpenseStatus {
  PENDING
  APPROVED
  REJECTED
  REIMBURSED
  CANCELLED
}

enum ReimbursementStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
}

enum PaymentMethod {
  BANK_TRANSFER
  CASH
  CHEQUE
}

enum PolicyRuleType {
  AMOUNT_LIMIT
  FREQUENCY_LIMIT
  APPROVAL_REQUIRED
  RECEIPT_REQUIRED
  GPS_REQUIRED
}

enum TravelMode {
  FLIGHT
  TRAIN
  BUS
  CAR
  TAXI
  OTHER
}

enum TravelRequestStatus {
  PENDING
  APPROVED
  REJECTED
  COMPLETED
  CANCELLED
}

enum ReviewType {
  QUARTERLY
  HALF_YEARLY
  ANNUAL
  PROBATION
  MID_YEAR
  PROJECT_BASED
}

enum ReviewStatus {
  DRAFT
  SUBMITTED
  IN_REVIEW
  COMPLETED
  CALIBRATED
  PUBLISHED
}

enum CycleStatus {
  DRAFT
  ACTIVE
  IN_PROGRESS
  CALIBRATION
  COMPLETED
  ARCHIVED
}

enum ObjectiveCategory {
  INDIVIDUAL
  TEAM
  DEPARTMENT
  COMPANY
  PROJECT
}

enum Priority {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum ObjectiveStatus {
  DRAFT
  ACTIVE
  ON_TRACK
  AT_RISK
  BEHIND
  COMPLETED
  CANCELLED
}

enum KeyResultType {
  QUANTITATIVE
  QUALITATIVE
  MILESTONE
  BINARY
}

enum KeyResultStatus {
  ACTIVE
  COMPLETED
  CANCELLED
  DEFERRED
}

enum ReviewerType {
  SELF
  MANAGER
  PEER
  SUBORDINATE
  EXTERNAL
  SKIP_LEVEL
}

enum FeedbackStatus {
  PENDING
  IN_PROGRESS
  SUBMITTED
  OVERDUE
}

enum QuestionType {
  RATING
  TEXT
  MULTIPLE_CHOICE
  YES_NO
  SCALE
}

enum DocumentCategory {
  PERSONAL
  PROFESSIONAL
  COMPLIANCE
  PAYROLL
  PERFORMANCE
  LEAVE
  EXPENSE
}

enum UserRole {
  ADMIN
  HR
  MANAGER
  FINANCE
  EMPLOYEE
}

enum OnboardingCategory {
  PERSONAL_INFO
  DOCUMENTS
  SYSTEM_ACCESS
  TRAINING
  COMPLIANCE
  EQUIPMENT
  INTRODUCTION
}

enum OnboardingStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  OVERDUE
  CANCELLED
}

enum TaskStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  SKIPPED
  OVERDUE
}

enum ApprovalStatus {
  PENDING
  APPROVED
  REJECTED
}

enum PayslipStatus {
  GENERATED
  ACCESSED
  DOWNLOADED
  SENT
}

enum AttendanceRequestStatus {
  PENDING
  APPROVED
  REJECTED
  FAILED
}

enum TimesheetStatus {
  DRAFT
  SUBMITTED
  APPROVED
  REJECTED
  CANCELLED
}

enum ProjectStatus {
  ACTIVE
  COMPLETED
  ON_HOLD
  CANCELLED
}

// Announcement Management
model Announcement {
  id          String            @id @default(cuid())
  title       String
  content     String            @db.Text
  type        AnnouncementType  @default(GENERAL)
  priority    AnnouncementPriority @default(NORMAL)
  status      AnnouncementStatus @default(DRAFT)
  publishedAt DateTime?
  expiresAt   DateTime?
  attachments Json?             // Array of attachment file URLs and metadata
  targetAudience Json?          // Array of department IDs, role IDs, or employee IDs
  isGlobal    Boolean           @default(false) // If true, visible to all employees
  viewCount   Int               @default(0)
  createdBy   String
  publishedBy String?
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt

  views AnnouncementView[]

  @@index([status, publishedAt])
  @@index([type, priority])
  @@index([createdBy])
  @@map("announcements")
}

model AnnouncementView {
  id             String   @id @default(cuid())
  announcementId String
  employeeId     String
  viewedAt       DateTime @default(now())

  announcement Announcement @relation(fields: [announcementId], references: [id], onDelete: Cascade)
  employee     Employee     @relation(fields: [employeeId], references: [id], onDelete: Cascade)

  @@unique([announcementId, employeeId])
  @@index([employeeId])
  @@map("announcement_views")
}

enum AnnouncementType {
  GENERAL
  POLICY
  EVENT
  HOLIDAY
  SYSTEM
  URGENT
  CELEBRATION
}

enum AnnouncementPriority {
  LOW
  NORMAL
  HIGH
  CRITICAL
}

enum AnnouncementStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
  EXPIRED
}

enum SiteType {
  CLIENT
  VENDOR
  PARTNER
  WAREHOUSE
  OFFICE
  OTHER
}

enum SiteVisitStatus {
  IN_PROGRESS
  COMPLETED
  CANCELLED
  MISSED
}

// Distance Tracking and Calculation System
model DistanceTrackingPoint {
  id                    String   @id @default(cuid())
  employeeId            String
  timestamp             DateTime @default(now())
  latitude              Decimal  @db.Decimal(10, 8)
  longitude             Decimal  @db.Decimal(11, 8)
  accuracy              Decimal? @db.Decimal(6, 2) // GPS accuracy in meters
  siteId                String?
  siteName              String?
  distanceFromPrevious  Decimal? @db.Decimal(10, 2) // Distance in meters
  durationFromPrevious  Int?     // Duration in seconds
  calculationMethod     DistanceCalculationMethod @default(HAVERSINE)
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  employee Employee @relation(fields: [employeeId], references: [id], onDelete: Cascade)

  @@index([employeeId, timestamp])
  @@index([timestamp])
  @@map("distance_tracking_points")
}

model DailyDistanceRecord {
  id            String   @id @default(cuid())
  employeeId    String
  date          DateTime @db.Date
  totalDistance Decimal  @db.Decimal(10, 2) @default(0) // Total distance in meters
  totalDuration Int?     // Total duration in seconds
  checkInCount  Int      @default(0) // Number of check-ins for the day
  isValidated   Boolean  @default(true) // Whether the record passed anomaly detection
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  employee  Employee          @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  anomalies DistanceAnomaly[]

  @@unique([employeeId, date], name: "employeeId_date")
  @@index([employeeId, date])
  @@index([date])
  @@map("daily_distance_records")
}

model DistanceAnomaly {
  id              String                @id @default(cuid())
  employeeId      String
  date            DateTime              @db.Date
  checkInPointId  String
  type            DistanceAnomalyType
  description     String
  severity        AnomalySeverity       @default(MEDIUM)
  isResolved      Boolean               @default(false)
  resolvedBy      String?
  resolvedAt      DateTime?
  resolutionNotes String?
  detectedAt      DateTime              @default(now())
  createdAt       DateTime              @default(now())
  updatedAt       DateTime              @updatedAt

  employee           Employee            @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  dailyDistanceRecord DailyDistanceRecord @relation(fields: [employeeId, date], references: [employeeId, date], onDelete: Cascade)

  @@index([employeeId, date])
  @@index([type, severity])
  @@index([isResolved])
  @@map("distance_anomalies")
}

// Distance Calculation Configuration
model DistanceCalculationConfig {
  id                        String   @id @default(cuid())
  maxSpeedKmh               Int      @default(120) // Maximum reasonable speed in km/h
  maxDistancePerDayKm       Int      @default(500) // Maximum distance per day in km
  minTimeBetweenCheckins    Int      @default(5)   // Minimum time between check-ins in minutes
  enableGoogleMatrixAPI     Boolean  @default(true)
  anomalyDetectionEnabled   Boolean  @default(true)
  googleMapsApiKey          String?  // Encrypted API key
  isActive                  Boolean  @default(true)
  createdAt                 DateTime @default(now())
  updatedAt                 DateTime @updatedAt

  @@map("distance_calculation_configs")
}

enum DistanceCalculationMethod {
  HAVERSINE
  GOOGLE_MATRIX
}

enum DistanceAnomalyType {
  EXCESSIVE_SPEED
  IMPOSSIBLE_DISTANCE
  LOCATION_JUMP
  MISSING_ROUTE
  DUPLICATE_CHECKIN
}

enum AnomalySeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}
